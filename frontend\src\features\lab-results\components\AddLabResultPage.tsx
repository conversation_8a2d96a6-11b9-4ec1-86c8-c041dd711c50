import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, User, Calendar, FileText, Beaker } from 'lucide-react';
import { useLabResults } from '../hooks/useLabResults';
import { usePatients } from '../../patients/hooks/usePatients';
import { useToast } from '../../../components/ui/Toast';
import type { LabResultFormData } from '../types';

const AddLabResultPage: React.FC = () => {
  const navigate = useNavigate();
  const { createLabResult, isCreating } = useLabResults();
  const { patients } = usePatients();
  const { success, error: showError } = useToast();
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<LabResultFormData>({
    patientId: '',
    testType: 'CBC',
    testDate: new Date().toISOString().split('T')[0],
    orderedBy: '',
    labName: '',
    results: {},
    normalRanges: {},
    flags: {},
    notes: '',
    status: 'COMPLETED'
  });

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate required fields
    if (!formData.patientId || !formData.testType || !formData.testDate || !formData.orderedBy) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      await createLabResult(formData);
      success('Lab Result Created', 'The lab result has been successfully added.');
      navigate('/lab-results');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create lab result';
      setError(errorMessage);
      showError('Failed to Create Lab Result', errorMessage);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Add Lab Result</h1>
        <Link
          to="/lab-results"
          className="text-gray-600 hover:text-gray-800 flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Lab Results
        </Link>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="patientId" className="block text-sm font-medium text-gray-700 mb-1">
                <User className="inline h-4 w-4 mr-1" />
                Patient *
              </label>
              <select
                id="patientId"
                name="patientId"
                value={formData.patientId}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Patient</option>
                {patients.map(patient => (
                  <option key={patient.id} value={patient.id}>
                    {patient.firstName} {patient.lastName} ({patient.patientId})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="testType" className="block text-sm font-medium text-gray-700 mb-1">
                <Beaker className="inline h-4 w-4 mr-1" />
                Test Type *
              </label>
              <select
                id="testType"
                name="testType"
                value={formData.testType}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Test Type</option>
                <option value="CBC">Complete Blood Count (CBC)</option>
                <option value="CMP">Comprehensive Metabolic Panel (CMP)</option>
                <option value="LIPID">Lipid Panel</option>
                <option value="TSH">Thyroid Stimulating Hormone (TSH)</option>
                <option value="HBA1C">Hemoglobin A1C</option>
                <option value="VITAMIN_D">Vitamin D</option>
                <option value="B12">Vitamin B12</option>
                <option value="FOLATE">Folate</option>
                <option value="IRON">Iron Studies</option>
                <option value="PSA">Prostate Specific Antigen (PSA)</option>
                <option value="OTHER">Other</option>
              </select>
            </div>

            <div>
              <label htmlFor="testDate" className="block text-sm font-medium text-gray-700 mb-1">
                <Calendar className="inline h-4 w-4 mr-1" />
                Test Date *
              </label>
              <input
                type="date"
                id="testDate"
                name="testDate"
                value={formData.testDate}
                onChange={handleChange}
                required
                max={new Date().toISOString().split('T')[0]}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label htmlFor="orderedBy" className="block text-sm font-medium text-gray-700 mb-1">
                Ordered By *
              </label>
              <input
                type="text"
                id="orderedBy"
                name="orderedBy"
                value={formData.orderedBy}
                onChange={handleChange}
                required
                placeholder="Dr. Smith"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label htmlFor="labName" className="block text-sm font-medium text-gray-700 mb-1">
                Lab Name
              </label>
              <input
                type="text"
                id="labName"
                name="labName"
                value={formData.labName}
                onChange={handleChange}
                placeholder="Quest Diagnostics"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="COMPLETED">Completed</option>
                <option value="PENDING">Pending</option>
                <option value="IN_PROGRESS">In Progress</option>
              </select>
            </div>
          </div>
        </div>

        {/* Notes */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-600" />
            Additional Notes
          </h2>
          <textarea
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Add any additional notes about the lab results, abnormal findings, or follow-up recommendations..."
          />
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          <Link
            to="/lab-results"
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isCreating}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4 mr-2" />
            {isCreating ? 'Creating...' : 'Create Lab Result'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddLabResultPage;
