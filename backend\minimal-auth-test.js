const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = 3003;

// Environment variables
const JWT_SECRET = 'your-super-secret-jwt-key-change-this-in-production';

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  if (req.headers.authorization) {
    console.log('Authorization header:', req.headers.authorization.substring(0, 30) + '...');
  }
  next();
});

// JWT utility functions
const generateAccessToken = (payload) => {
  console.log('🔑 Generating token for payload:', payload);
  const token = jwt.sign(payload, JWT_SECRET, {
    expiresIn: '15m',
    issuer: 'psychiatry-app',
    audience: 'psychiatry-app-users',
  });
  console.log('✅ Token generated, length:', token.length);
  return token;
};

const verifyAccessToken = (token) => {
  try {
    console.log('🔍 Verifying token...');
    const payload = jwt.verify(token, JWT_SECRET, {
      issuer: 'psychiatry-app',
      audience: 'psychiatry-app-users',
    });
    console.log('✅ Token verified, payload:', payload);
    return payload;
  } catch (error) {
    console.error('❌ Token verification error:', error.message);
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Access token expired');
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid access token');
    }
    throw new Error('Token verification failed');
  }
};

const extractTokenFromHeader = (authHeader) => {
  console.log('🔍 Extracting token from header:', authHeader);
  if (!authHeader) {
    console.log('❌ No authorization header');
    return null;
  }
  
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    console.log('❌ Invalid authorization header format');
    return null;
  }
  
  console.log('✅ Token extracted successfully');
  return parts[1];
};

// Authentication middleware
const authenticate = async (req, res, next) => {
  try {
    console.log('\n🔐 AUTHENTICATION MIDDLEWARE TRIGGERED');
    console.log('Request headers:', req.headers);
    
    const token = extractTokenFromHeader(req.headers.authorization);

    if (!token) {
      console.log('❌ No token provided');
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }

    // Verify the token
    const payload = verifyAccessToken(token);

    // Mock user data (since we're not using database)
    req.user = {
      id: payload.userId,
      username: payload.username,
      role: payload.role,
    };

    console.log('✅ Authentication successful for user:', payload.username);
    next();
  } catch (error) {
    console.error('❌ Authentication error:', error.message);
    res.status(401).json({
      success: false,
      message: error.message || 'Authentication failed'
    });
  }
};

// Health check
app.get('/health', (req, res) => {
  console.log('💚 Health check requested');
  res.status(200).json({
    success: true,
    message: 'Minimal auth test server is healthy',
    timestamp: new Date().toISOString(),
  });
});

// Login endpoint (mock)
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('\n🔑 LOGIN ATTEMPT');
    console.log('Request body:', req.body);
    
    const { username, password } = req.body;

    if (!username || !password) {
      console.log('❌ Missing credentials');
      return res.status(400).json({
        success: false,
        message: 'Username and password are required'
      });
    }

    // Mock authentication (accept admin/admin123)
    if (username === 'admin' && password === 'admin123') {
      console.log('✅ Mock authentication successful');
      
      // Generate token
      const accessToken = generateAccessToken({
        userId: 'mock-user-id',
        username: username,
        role: 'ADMIN',
      });

      console.log('✅ Login successful for user:', username);

      res.status(200).json({
        success: true,
        data: {
          user: {
            id: 'mock-user-id',
            username: username,
            role: 'ADMIN',
          },
          accessToken,
        },
        message: 'Login successful',
      });
    } else {
      console.log('❌ Invalid credentials');
      res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

  } catch (error) {
    console.error('❌ Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Protected endpoint test
app.get('/api/patients', authenticate, async (req, res) => {
  try {
    console.log('\n📋 PROTECTED ENDPOINT ACCESS');
    console.log('User:', req.user);
    
    // Mock patients data
    const patients = [
      {
        id: 'patient-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      }
    ];

    console.log('✅ Returning mock patients data');

    res.status(200).json({
      success: true,
      data: { patients },
      message: 'Patients retrieved successfully'
    });
  } catch (error) {
    console.error('❌ Error in patients endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch patients'
    });
  }
});

// Dashboard analytics test
app.get('/api/analytics/dashboard', authenticate, async (req, res) => {
  try {
    console.log('\n📊 DASHBOARD ANALYTICS ACCESS');
    console.log('User:', req.user);
    
    const analytics = {
      totalPatients: 1,
      totalAppointments: 0,
      totalLabResults: 0,
      flaggedLabResults: 0,
    };

    console.log('✅ Returning mock analytics data');

    res.status(200).json({
      success: true,
      data: analytics,
      message: 'Dashboard analytics retrieved successfully'
    });
  } catch (error) {
    console.error('❌ Error in analytics endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard analytics'
    });
  }
});

// Error handler
app.use((error, req, res, next) => {
  console.error('❌ Unhandled error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

// Start server
app.listen(PORT, () => {
  console.log('\n🚀 MINIMAL AUTH TEST SERVER RUNNING');
  console.log(`📍 Port: ${PORT}`);
  console.log(`🔗 Health: http://localhost:${PORT}/health`);
  console.log(`🔑 Login: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`📋 Patients: GET http://localhost:${PORT}/api/patients`);
  console.log(`📊 Analytics: GET http://localhost:${PORT}/api/analytics/dashboard`);
  console.log('\n🧪 TEST CREDENTIALS: admin / admin123');
  console.log('🔍 READY FOR AUTHENTICATION TESTING...\n');
});

module.exports = app;
