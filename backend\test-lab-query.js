const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testLabQuery() {
  try {
    console.log('Testing lab result query...');
    const count = await prisma.labResult.count({ 
      where: { 
        isDeleted: false, 
        flagged: true 
      } 
    });
    console.log('Flagged lab results count:', count);
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testLabQuery();
