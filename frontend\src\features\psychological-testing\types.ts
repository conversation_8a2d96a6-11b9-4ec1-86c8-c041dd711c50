export interface TestItem {
  id: number;
  question: string;
  options: string[];
  scores: number[];
  category?: string;
  riskFlag?: boolean; // For items that indicate risk (e.g., suicidal ideation)
}

export interface TestDefinition {
  id: string;
  name: string;
  category: string;
  description: string;
  items: TestItem[];
  scoringRules: ScoringRules;
  interpretationRules: InterpretationRules;
  validityChecks?: ValidityCheck[];
  timeLimit?: number; // in minutes
  instructions?: string;
}

export interface ScoringRules {
  totalScore: {
    method: 'sum' | 'average' | 'weighted';
    weights?: number[];
  };
  subscales?: {
    [subscaleName: string]: {
      items: number[];
      method: 'sum' | 'average';
    };
  };
  reversedItems?: number[]; // Items that need reverse scoring
}

export interface InterpretationRules {
  severity: {
    ranges: Array<{
      min: number;
      max: number;
      label: string;
      description: string;
      color: string;
    }>;
  };
  clinicalCutoffs?: {
    [cutoffName: string]: {
      threshold: number;
      direction: 'above' | 'below';
      meaning: string;
    };
  };
  recommendations?: {
    [severityLevel: string]: string[];
  };
}

export interface ValidityCheck {
  type: 'consistency' | 'response_pattern' | 'time_based';
  rule: string;
  threshold?: number;
  message: string;
}

export interface TestResponse {
  itemId: number;
  response: number;
  responseTime?: number; // in seconds
  skipped?: boolean;
}

export interface TestSession {
  id: string;
  patientId: string;
  testId: string;
  administeredBy: string;
  startTime: string;
  endTime?: string;
  responses: TestResponse[];
  status: 'in_progress' | 'completed' | 'abandoned' | 'invalid';
  environment: 'clinical' | 'remote' | 'self_administered';
  notes?: string;
}

export interface TestResult {
  sessionId: string;
  testId: string;
  patientId: string;
  administeredBy: string;
  completedAt: string;
  
  // Scoring
  totalScore: number;
  subscaleScores?: { [subscale: string]: number };
  percentile?: number;
  tScore?: number;
  zScore?: number;
  
  // Interpretation
  severity: string;
  clinicalRange: 'normal' | 'borderline' | 'clinical';
  interpretation: string;
  recommendations: string[];
  
  // Validity
  validity: 'valid' | 'questionable' | 'invalid';
  validityFlags?: string[];
  
  // Risk Assessment
  riskFlags?: {
    type: 'suicide' | 'self_harm' | 'violence' | 'substance_abuse';
    level: 'low' | 'moderate' | 'high' | 'imminent';
    items: number[];
    description: string;
  }[];
  
  // Metadata
  completionTime: number; // in minutes
  responsePattern: 'consistent' | 'inconsistent' | 'extreme_responding';
  followUpRecommended: boolean;
}

export interface TestBattery {
  id: string;
  name: string;
  description: string;
  tests: string[]; // Array of test IDs
  estimatedTime: number; // in minutes
  purpose: string;
  targetPopulation?: string;
  contraindications?: string[];
}

export interface PatientTestHistory {
  patientId: string;
  tests: Array<{
    testId: string;
    testName: string;
    completedAt: string;
    totalScore: number;
    severity: string;
    validity: string;
    sessionId: string;
  }>;
  trends?: {
    testId: string;
    scores: Array<{
      date: string;
      score: number;
      severity: string;
    }>;
    trend: 'improving' | 'stable' | 'worsening';
    significance: 'significant' | 'minimal' | 'none';
  }[];
}

// Specific test types
export type TestCategory = 
  | 'depression'
  | 'anxiety' 
  | 'trauma'
  | 'psychosis'
  | 'cognitive'
  | 'personality'
  | 'substance_use'
  | 'eating_disorders'
  | 'sleep'
  | 'adhd'
  | 'autism'
  | 'mood'
  | 'ocd'
  | 'general_screening';

export interface TestAdministrationSettings {
  allowSkipping: boolean;
  showProgress: boolean;
  randomizeItems: boolean;
  timeWarnings: boolean;
  autoSave: boolean;
  requireAllItems: boolean;
}
