import React, { Component, type ReactNode } from 'react';
import { Alert<PERSON>riangle, RefreshCw, WifiOff } from 'lucide-react';

interface ApiErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  retryCount: number;
  isRetrying: boolean;
}

interface ApiErrorBoundaryProps {
  children: ReactNode;
  maxRetries?: number;
  onError?: (error: Error) => void;
  fallback?: (error: Error, retry: () => void) => ReactNode;
}

/**
 * Enhanced Error Boundary specifically designed for API-related errors
 * Provides automatic retry functionality and better error categorization
 */
export class ApiErrorBoundary extends Component<ApiErrorBoundaryProps, ApiErrorBoundaryState> {
  private retryTimeout: NodeJS.Timeout | null = null;

  constructor(props: ApiErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      retryCount: 0,
      isRetrying: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ApiErrorBoundaryState> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('API Error Boundary caught error:', error, errorInfo);
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error);
    }

    // Auto-retry for network errors
    if (this.isNetworkError(error) && this.state.retryCount < (this.props.maxRetries || 3)) {
      this.scheduleRetry();
    }
  }

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
  }

  isNetworkError = (error: Error): boolean => {
    const networkErrorPatterns = [
      'network error',
      'fetch',
      'connection',
      'timeout',
      'ECONNABORTED',
      'ERR_NETWORK'
    ];
    
    return networkErrorPatterns.some(pattern => 
      error.message.toLowerCase().includes(pattern.toLowerCase())
    );
  };

  isApiError = (error: Error): boolean => {
    const apiErrorPatterns = [
      '404',
      '500',
      '401',
      '403',
      'api',
      'server error',
      'bad request'
    ];
    
    return apiErrorPatterns.some(pattern => 
      error.message.toLowerCase().includes(pattern.toLowerCase())
    );
  };

  scheduleRetry = () => {
    this.setState({ isRetrying: true });
    
    // Exponential backoff: 1s, 2s, 4s
    const delay = Math.pow(2, this.state.retryCount) * 1000;
    
    this.retryTimeout = setTimeout(() => {
      this.handleRetry();
    }, delay);
  };

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      retryCount: prevState.retryCount + 1,
      isRetrying: false
    }));
  };

  handleManualRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      retryCount: 0,
      isRetrying: false
    });
  };

  getErrorType = (): 'network' | 'api' | 'unknown' => {
    if (!this.state.error) return 'unknown';
    
    if (this.isNetworkError(this.state.error)) return 'network';
    if (this.isApiError(this.state.error)) return 'api';
    return 'unknown';
  };

  renderErrorUI = () => {
    const { error, retryCount, isRetrying } = this.state;
    const { maxRetries = 3 } = this.props;
    const errorType = this.getErrorType();

    if (this.props.fallback && error) {
      return this.props.fallback(error, this.handleManualRetry);
    }

    const getErrorConfig = () => {
      switch (errorType) {
        case 'network':
          return {
            icon: <WifiOff className="w-8 h-8 text-red-600" />,
            title: 'Connection Problem',
            message: 'Unable to connect to the server. Please check your internet connection.',
            canRetry: true
          };
        case 'api':
          return {
            icon: <AlertTriangle className="w-8 h-8 text-orange-600" />,
            title: 'Server Error',
            message: 'The server encountered an error. This might be temporary.',
            canRetry: true
          };
        default:
          return {
            icon: <AlertTriangle className="w-8 h-8 text-red-600" />,
            title: 'Something went wrong',
            message: 'An unexpected error occurred. Please try again.',
            canRetry: false
          };
      }
    };

    const config = getErrorConfig();

    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mb-4">
          {config.icon}
        </div>
        
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {config.title}
        </h3>
        
        <p className="text-gray-600 mb-4 max-w-sm">
          {config.message}
        </p>

        {process.env.NODE_ENV === 'development' && error && (
          <details className="mb-4 text-left">
            <summary className="text-sm text-gray-500 cursor-pointer">
              Error Details (Development)
            </summary>
            <pre className="text-xs text-red-600 mt-2 p-2 bg-red-50 rounded overflow-auto max-w-md">
              {error.message}
            </pre>
          </details>
        )}

        {isRetrying ? (
          <div className="flex items-center text-blue-600">
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            <span className="text-sm">Retrying... ({retryCount + 1}/{maxRetries})</span>
          </div>
        ) : config.canRetry && (
          <button
            onClick={this.handleManualRetry}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
        )}

        {retryCount >= maxRetries && (
          <p className="text-sm text-gray-500 mt-2">
            Maximum retry attempts reached. Please refresh the page or contact support.
          </p>
        )}
      </div>
    );
  };

  render() {
    if (this.state.hasError) {
      return this.renderErrorUI();
    }

    return this.props.children;
  }
}

export default ApiErrorBoundary;
