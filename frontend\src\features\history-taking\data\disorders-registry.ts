import type { Disorder, DisorderCategory } from '../types';
import { MOOD_DISORDERS } from './mood-disorders';
import { PSYCHOTIC_DISORDERS } from './psychotic-disorders';
import { ANXIETY_DISORDERS } from './anxiety-disorders';
import { TRAUMA_STRESSOR_DISORDERS } from './trauma-stressor-disorders';
import { OBSESSIVE_COMPULSIVE_DISORDERS } from './obsessive-compulsive-disorders';

// Central registry of all DSM-5-TR disorders
export const DISORDERS_REGISTRY: Record<string, Disorder> = {};

// Category mappings
export const DISORDER_CATEGORIES: Record<DisorderCategory, { name: string; description: string; disorders: string[] }> = {
  'neurodevelopmental': {
    name: 'Neurodevelopmental Disorders',
    description: 'Disorders that typically manifest early in development',
    disorders: []
  },
  'schizophrenia-spectrum': {
    name: 'Schizophrenia Spectrum and Other Psychotic Disorders',
    description: 'Disorders characterized by psychotic symptoms',
    disorders: []
  },
  'bipolar-related': {
    name: 'Bipolar and Related Disorders',
    description: 'Disorders characterized by manic or hypomanic episodes',
    disorders: []
  },
  'depressive': {
    name: 'Depressive Disorders',
    description: 'Disorders characterized by depressed mood',
    disorders: []
  },
  'anxiety': {
    name: 'Anxiety Disorders',
    description: 'Disorders characterized by excessive fear and anxiety',
    disorders: ['gad', 'panic-disorder']
  },
  'obsessive-compulsive': {
    name: 'Obsessive-Compulsive and Related Disorders',
    description: 'Disorders characterized by obsessions and compulsions',
    disorders: ['ocd', 'bdd', 'hoarding']
  },
  'trauma-stressor': {
    name: 'Trauma- and Stressor-Related Disorders',
    description: 'Disorders following exposure to traumatic or stressful events',
    disorders: ['ptsd']
  },
  'dissociative': {
    name: 'Dissociative Disorders',
    description: 'Disorders characterized by disruption of consciousness, memory, identity, or perception',
    disorders: []
  },
  'somatic-symptom': {
    name: 'Somatic Symptom and Related Disorders',
    description: 'Disorders characterized by prominent somatic symptoms',
    disorders: []
  },
  'feeding-eating': {
    name: 'Feeding and Eating Disorders',
    description: 'Disorders characterized by persistent disturbance of eating',
    disorders: []
  },
  'elimination': {
    name: 'Elimination Disorders',
    description: 'Disorders characterized by inappropriate elimination',
    disorders: []
  },
  'sleep-wake': {
    name: 'Sleep-Wake Disorders',
    description: 'Disorders characterized by sleep disturbances',
    disorders: []
  },
  'sexual-dysfunctions': {
    name: 'Sexual Dysfunctions',
    description: 'Disorders characterized by sexual dysfunction',
    disorders: []
  },
  'gender-dysphoria': {
    name: 'Gender Dysphoria',
    description: 'Disorders characterized by gender dysphoria',
    disorders: []
  },
  'disruptive-impulse': {
    name: 'Disruptive, Impulse-Control, and Conduct Disorders',
    description: 'Disorders characterized by problems in emotional and behavioral self-control',
    disorders: []
  },
  'substance-related': {
    name: 'Substance-Related and Addictive Disorders',
    description: 'Disorders related to substance use and addiction',
    disorders: []
  },
  'neurocognitive': {
    name: 'Neurocognitive Disorders',
    description: 'Disorders characterized by cognitive decline',
    disorders: []
  },
  'personality': {
    name: 'Personality Disorders',
    description: 'Disorders characterized by enduring patterns of behavior and inner experience',
    disorders: []
  },
  'paraphilic': {
    name: 'Paraphilic Disorders',
    description: 'Disorders characterized by recurrent, intense sexually arousing fantasies, urges, or behaviors',
    disorders: []
  },
  'other-mental': {
    name: 'Other Mental Disorders',
    description: 'Mental disorders that do not meet criteria for other categories',
    disorders: []
  },
  'medication-induced': {
    name: 'Medication-Induced Movement Disorders and Other Adverse Effects of Medication',
    description: 'Disorders caused by medication side effects',
    disorders: []
  },
  'other-conditions': {
    name: 'Other Conditions That May Be a Focus of Clinical Attention',
    description: 'Conditions that may be a focus of clinical attention but are not mental disorders',
    disorders: []
  }
};

// Initialize registry with disorders
function initializeRegistry() {
  // Add mood disorders
  MOOD_DISORDERS.forEach(disorder => {
    DISORDERS_REGISTRY[disorder.id] = disorder;
    if (!DISORDER_CATEGORIES[disorder.category].disorders.includes(disorder.id)) {
      DISORDER_CATEGORIES[disorder.category].disorders.push(disorder.id);
    }
  });

  // Add psychotic disorders
  PSYCHOTIC_DISORDERS.forEach(disorder => {
    DISORDERS_REGISTRY[disorder.id] = disorder;
    if (!DISORDER_CATEGORIES[disorder.category].disorders.includes(disorder.id)) {
      DISORDER_CATEGORIES[disorder.category].disorders.push(disorder.id);
    }
  });

  // Add anxiety disorders
  ANXIETY_DISORDERS.forEach(disorder => {
    DISORDERS_REGISTRY[disorder.id] = disorder;
    if (!DISORDER_CATEGORIES[disorder.category].disorders.includes(disorder.id)) {
      DISORDER_CATEGORIES[disorder.category].disorders.push(disorder.id);
    }
  });

  // Add trauma and stressor-related disorders
  TRAUMA_STRESSOR_DISORDERS.forEach(disorder => {
    DISORDERS_REGISTRY[disorder.id] = disorder;
    if (!DISORDER_CATEGORIES[disorder.category].disorders.includes(disorder.id)) {
      DISORDER_CATEGORIES[disorder.category].disorders.push(disorder.id);
    }
  });

  // Add obsessive-compulsive and related disorders
  OBSESSIVE_COMPULSIVE_DISORDERS.forEach(disorder => {
    DISORDERS_REGISTRY[disorder.id] = disorder;
    if (!DISORDER_CATEGORIES[disorder.category].disorders.includes(disorder.id)) {
      DISORDER_CATEGORIES[disorder.category].disorders.push(disorder.id);
    }
  });
}

// Initialize the registry
initializeRegistry();

// Utility functions
export function getDisorderById(id: string): Disorder | undefined {
  return DISORDERS_REGISTRY[id];
}

export function getDisordersByCategory(category: DisorderCategory): Disorder[] {
  const disorderIds = DISORDER_CATEGORIES[category].disorders;
  return disorderIds.map(id => DISORDERS_REGISTRY[id]).filter(Boolean);
}

export function getAllDisorders(): Disorder[] {
  return Object.values(DISORDERS_REGISTRY);
}

export function searchDisorders(query: string): Disorder[] {
  const lowercaseQuery = query.toLowerCase();
  return getAllDisorders().filter(disorder =>
    disorder.name.toLowerCase().includes(lowercaseQuery) ||
    disorder.description.toLowerCase().includes(lowercaseQuery) ||
    disorder.code.toLowerCase().includes(lowercaseQuery) ||
    disorder.diagnosticFeatures.some(feature => 
      feature.toLowerCase().includes(lowercaseQuery)
    )
  );
}

export function getDisordersBySymptoms(symptoms: string[]): Disorder[] {
  const lowercaseSymptoms = symptoms.map(s => s.toLowerCase());
  
  return getAllDisorders().filter(disorder => {
    const disorderText = [
      disorder.name,
      disorder.description,
      ...disorder.diagnosticFeatures,
      ...disorder.criteria.map(c => c.description),
      ...disorder.criteria.flatMap(c => c.subCriteria?.map(sc => sc.description) || [])
    ].join(' ').toLowerCase();

    return lowercaseSymptoms.some(symptom => 
      disorderText.includes(symptom)
    );
  });
}

export function validateDisorderCriteria(disorderId: string, criteria: Record<string, boolean>): {
  isValid: boolean;
  metCriteria: string[];
  missingRequired: string[];
  canDiagnose: boolean;
} {
  const disorder = getDisorderById(disorderId);
  if (!disorder) {
    return {
      isValid: false,
      metCriteria: [],
      missingRequired: [],
      canDiagnose: false
    };
  }

  const metCriteria: string[] = [];
  const missingRequired: string[] = [];

  // Check each criterion
  disorder.criteria.forEach(criterion => {
    const isMet = criteria[criterion.id] || false;
    
    if (isMet) {
      metCriteria.push(criterion.id);
    } else if (criterion.required) {
      missingRequired.push(criterion.id);
    }

    // Check sub-criteria if present
    if (criterion.subCriteria) {
      const subCriteriaMet = criterion.subCriteria.filter(sc => criteria[sc.id]).length;
      const minRequired = criterion.validationRules?.find(r => r.type === 'minimum')?.value || 1;
      
      if (subCriteriaMet < minRequired && criterion.required) {
        missingRequired.push(criterion.id);
      }
    }
  });

  const isValid = missingRequired.length === 0;
  const canDiagnose = isValid && metCriteria.length >= (disorder.minimumCriteria || 1);

  return {
    isValid,
    metCriteria,
    missingRequired,
    canDiagnose
  };
}

export function generateDiagnosticSuggestions(symptoms: string[]): Array<{
  disorder: Disorder;
  confidence: number;
  reasoning: string;
}> {
  const suggestions = getDisordersBySymptoms(symptoms).map(disorder => {
    // Simple confidence calculation based on symptom matches
    const disorderText = [
      disorder.name,
      disorder.description,
      ...disorder.diagnosticFeatures
    ].join(' ').toLowerCase();

    const matchCount = symptoms.filter(symptom => 
      disorderText.includes(symptom.toLowerCase())
    ).length;

    const confidence = Math.min(matchCount / symptoms.length, 1);

    return {
      disorder,
      confidence,
      reasoning: `Matches ${matchCount} of ${symptoms.length} reported symptoms`
    };
  });

  // Sort by confidence and return top suggestions
  return suggestions
    .sort((a, b) => b.confidence - a.confidence)
    .slice(0, 5);
}

// DISORDERS_REGISTRY is already exported at the top of the fileis already exported at the top of the file
