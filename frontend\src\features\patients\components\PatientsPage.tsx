import React, { useState, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FileText, Plus, Search, Eye, Brain, Edit, Trash2 } from 'lucide-react';
import { usePatients } from '../hooks/usePatients';
import type { Patient } from '../types';
import { Table, Button, Badge } from '../../../components/ui';
import type { Column } from '../../../components/ui';
import { useDebounce } from '../../../lib/performance';
import LoadingSpinner from '../../../components/ui/LoadingSpinner';
import { useToast } from '../../../components/ui/Toast';

const PatientsPage: React.FC = () => {
  const navigate = useNavigate();
  const { success, error: showError } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Debounce search term for better performance
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const filters = useMemo(() => ({
    search: debouncedSearchTerm,
  }), [debouncedSearchTerm]);

  const { patients, loading, error, deletePatient } = usePatients(filters);

  // Memoized filtered and sorted patients
  const processedPatients = useMemo(() => {
    let result = [...patients];

    // Apply sorting
    if (sortBy) {
      result.sort((a, b) => {
        const aValue = a[sortBy as keyof Patient];
        const bValue = b[sortBy as keyof Patient];

        if (aValue && bValue) {
          if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
          if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return result;
  }, [patients, sortBy, sortOrder]);

  const handleExportCsv = () => {
    window.open('http://localhost:3002/api/export/patients', '_blank');
  };

  const handleDeletePatient = async (patientId: string, patientName: string) => {
    if (window.confirm(`Are you sure you want to delete patient "${patientName}"? This action cannot be undone.`)) {
      try {
        await deletePatient(patientId);
        success('Patient Deleted', `Patient "${patientName}" has been successfully deleted.`);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to delete patient';
        showError('Failed to Delete Patient', errorMessage);
      }
    }
  };

  const handleSort = (key: string, order: 'asc' | 'desc') => {
    setSortBy(key);
    setSortOrder(order);
  };

  // Define table columns
  const columns: Column<Patient>[] = useMemo(() => [
    {
      key: 'name',
      title: 'Name',
      sortable: true,
      render: (_, record) => (
        <div className="flex items-center space-x-3">
          <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 font-medium text-sm">
              {record.firstName[0]}{record.lastName[0]}
            </span>
          </div>
          <div>
            <p className="font-medium text-gray-900">
              {record.firstName} {record.lastName}
            </p>
            <p className="text-sm text-gray-500">ID: {record.patientId}</p>
          </div>
        </div>
      ),
    },
    {
      key: 'dateOfBirth',
      title: 'Date of Birth',
      dataIndex: 'dateOfBirth',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'gender',
      title: 'Gender',
      dataIndex: 'gender',
      sortable: true,
      render: (value) => value || 'Not specified',
    },
    {
      key: 'phone',
      title: 'Phone',
      dataIndex: 'phone',
      render: (value) => value || 'Not provided',
    },
    {
      key: 'status',
      title: 'Status',
      dataIndex: 'isActive',
      sortable: true,
      render: (value) => (
        <Badge variant={value ? 'default' : 'destructive'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, record) => (
        <div className="flex space-x-1">
          <Button
            variant="ghost"
            size="sm"
            leftIcon={<Eye className="h-4 w-4" />}
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/patients/${record.id}`);
            }}
            title="View Details"
          >
            View
          </Button>
          <Button
            variant="ghost"
            size="sm"
            leftIcon={<Edit className="h-4 w-4" />}
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/patients/${record.id}/edit`);
            }}
            title="Edit Patient"
          >
            Edit
          </Button>
          <Button
            variant="ghost"
            size="sm"
            leftIcon={<Trash2 className="h-4 w-4" />}
            onClick={(e) => {
              e.stopPropagation();
              handleDeletePatient(record.id, `${record.firstName} ${record.lastName}`);
            }}
            title="Delete Patient"
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            Delete
          </Button>
          <Button
            variant="ghost"
            size="sm"
            leftIcon={<Brain className="h-4 w-4" />}
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/history-taking/${record.id}`);
            }}
            title="Clinical Assessment"
          >
            Review
          </Button>
        </div>
      ),
    },
  ], []);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Patients</h1>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={handleExportCsv}
            leftIcon={<FileText className="h-4 w-4" />}
          >
            Export CSV
          </Button>
          <Link to="/patients/new">
            <Button leftIcon={<Plus className="h-4 w-4" />}>
              Add Patient
            </Button>
          </Link>
        </div>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <input
          type="text"
          placeholder="Search patients..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Patients Table */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">
            Patient List ({processedPatients.length})
          </h2>
        </div>

        <Table
          columns={columns}
          data={processedPatients}
          loading={loading}
          emptyText={searchTerm ? 'No patients found matching your search.' : 'No patients registered yet.'}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSort={handleSort}
          hoverable
          onRowClick={(patient) => {
            console.log('Row clicked:', patient);
            // Navigate to patient details
          }}
        />
      </div>
    </div>
  );
};

export default PatientsPage;
