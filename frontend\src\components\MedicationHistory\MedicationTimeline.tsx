import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>it<PERSON> } from '../ui/card';
import { Button } from '../ui/button';
import { Plus, Pill, Calendar, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

interface MedicationTimelineProps {
  patientId: string;
}

// Mock data for demonstration
const mockMedications = [
  {
    id: '1',
    medicationName: 'Sertraline',
    genericName: 'Sertraline HCl',
    brandName: 'Zoloft',
    strength: '50mg',
    dosage: '50mg',
    frequency: 'Once daily',
    route: 'PO',
    indication: 'Major Depressive Disorder',
    startDate: '2024-01-15',
    endDate: null,
    prescribedBy: 'Dr<PERSON> <PERSON>',
    effectiveness: 'good',
    adherence: 'excellent',
    isActive: true,
    sideEffects: ['Mild nausea', 'Initial drowsiness'],
    notes: 'Patient tolerating well, mood improvement noted'
  },
  {
    id: '2',
    medicationName: 'Lorazepam',
    genericName: 'Lorazepam',
    brandName: 'Ativan',
    strength: '0.5mg',
    dosage: '0.5mg',
    frequency: 'As needed',
    route: 'PO',
    indication: 'Anxiety',
    startDate: '2024-01-20',
    endDate: '2024-02-20',
    prescribedBy: 'Dr. Smith',
    effectiveness: 'excellent',
    adherence: 'good',
    isActive: false,
    discontinuedReason: 'Short-term use completed',
    sideEffects: ['Mild sedation'],
    notes: 'Used for acute anxiety episodes'
  },
  {
    id: '3',
    medicationName: 'Trazodone',
    genericName: 'Trazodone HCl',
    brandName: 'Desyrel',
    strength: '50mg',
    dosage: '25mg',
    frequency: 'At bedtime',
    route: 'PO',
    indication: 'Insomnia',
    startDate: '2024-02-01',
    endDate: null,
    prescribedBy: 'Dr. Smith',
    effectiveness: 'good',
    adherence: 'excellent',
    isActive: true,
    sideEffects: [],
    notes: 'Helping with sleep initiation'
  }
];

export const MedicationTimeline: React.FC<MedicationTimelineProps> = ({ patientId: _ }) => {
  const [medications] = useState(mockMedications);
  const [filter, setFilter] = useState<'all' | 'active' | 'discontinued'>('all');

  const filteredMedications = medications.filter(med => {
    if (filter === 'active') return med.isActive;
    if (filter === 'discontinued') return !med.isActive;
    return true;
  });

  const getEffectivenessColor = (effectiveness: string) => {
    switch (effectiveness) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'fair': return 'text-yellow-600 bg-yellow-100';
      case 'poor': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getAdherenceIcon = (adherence: string) => {
    switch (adherence) {
      case 'excellent': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'good': return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case 'fair': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'poor': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Medication History</h2>
          <p className="text-gray-600">Timeline of prescribed medications</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          Add Medication
        </Button>
      </div>

      {/* Filters */}
      <div className="flex space-x-2">
        <Button
          variant={filter === 'all' ? 'default' : 'outline'}
          onClick={() => setFilter('all')}
          size="sm"
        >
          All Medications
        </Button>
        <Button
          variant={filter === 'active' ? 'default' : 'outline'}
          onClick={() => setFilter('active')}
          size="sm"
        >
          Active
        </Button>
        <Button
          variant={filter === 'discontinued' ? 'default' : 'outline'}
          onClick={() => setFilter('discontinued')}
          size="sm"
        >
          Discontinued
        </Button>
      </div>

      {/* Timeline */}
      <div className="relative">
        {/* Timeline line */}
        <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-300"></div>

        {/* Medications */}
        <div className="space-y-6">
          {filteredMedications.map((medication) => (
            <div key={medication.id} className="relative flex items-start space-x-6">
              {/* Timeline dot */}
              <div className={`flex-shrink-0 w-4 h-4 rounded-full border-2 ${
                medication.isActive 
                  ? 'bg-green-500 border-green-500' 
                  : 'bg-gray-300 border-gray-300'
              }`}></div>

              {/* Medication card */}
              <Card className={`flex-1 ${medication.isActive ? 'border-green-200' : 'border-gray-200'}`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Pill className={`h-5 w-5 ${medication.isActive ? 'text-green-600' : 'text-gray-400'}`} />
                      <div>
                        <CardTitle className="text-lg">
                          {medication.medicationName} {medication.strength}
                        </CardTitle>
                        <p className="text-sm text-gray-600">
                          {medication?.brandName && `(${medication.brandName}) `}
                          {medication?.genericName}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        medication.isActive 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {medication.isActive ? 'Active' : 'Discontinued'}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Dosing information */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">Dosage:</span>
                      <p>{medication.dosage} {medication.frequency}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Route:</span>
                      <p>{medication.route}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Indication:</span>
                      <p>{medication.indication}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Prescribed by:</span>
                      <p>{medication.prescribedBy}</p>
                    </div>
                  </div>

                  {/* Timeline */}
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">Started:</span>
                      <span>{new Date(medication.startDate).toLocaleDateString()}</span>
                    </div>
                    {medication.endDate && (
                      <div className="flex items-center space-x-1">
                        <span className="font-medium">Ended:</span>
                        <span>{new Date(medication.endDate).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>

                  {/* Effectiveness and Adherence */}
                  <div className="flex items-center space-x-6">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-700">Effectiveness:</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getEffectivenessColor(medication.effectiveness)}`}>
                        {medication.effectiveness}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-700">Adherence:</span>
                      <div className="flex items-center space-x-1">
                        {getAdherenceIcon(medication.adherence)}
                        <span className="text-sm capitalize">{medication.adherence}</span>
                      </div>
                    </div>
                  </div>

                  {/* Side effects */}
                  {medication.sideEffects && medication.sideEffects.length > 0 && (
                    <div>
                      <span className="text-sm font-medium text-gray-700">Side Effects:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {medication.sideEffects.map((effect, idx) => (
                          <span key={idx} className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded">
                            {effect}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Discontinuation reason */}
                  {medication.discontinuedReason && (
                    <div>
                      <span className="text-sm font-medium text-gray-700">Discontinuation Reason:</span>
                      <p className="text-sm text-gray-600">{medication.discontinuedReason}</p>
                    </div>
                  )}

                  {/* Notes */}
                  {medication.notes && (
                    <div>
                      <span className="text-sm font-medium text-gray-700">Notes:</span>
                      <p className="text-sm text-gray-600">{medication.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>

      {/* Empty state */}
      {filteredMedications.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Pill className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No medications found</h3>
            <p className="text-gray-600 mb-4">
              {filter === 'all' 
                ? 'This patient has no medication history yet.' 
                : `No ${filter} medications found.`}
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add First Medication
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
