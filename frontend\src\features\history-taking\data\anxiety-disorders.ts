import type { Disorder } from '../types';

export const GENERALIZED_ANXIETY_DISORDER: Disorder = {
  id: 'gad',
  code: '300.02',
  name: 'Generalized Anxiety Disorder',
  category: 'anxiety',
  description: 'Excessive anxiety and worry occurring more days than not for at least 6 months, about a number of events or activities.',
  criteria: [
    {
      id: 'gad-a',
      code: 'A',
      description: 'Excessive anxiety and worry (apprehensive expectation), occurring more days than not for at least 6 months, about a number of events or activities (such as work or school performance).',
      required: true,
      type: 'symptom'
    },
    {
      id: 'gad-b',
      code: 'B',
      description: 'The individual finds it difficult to control the worry.',
      required: true,
      type: 'symptom'
    },
    {
      id: 'gad-c',
      code: 'C',
      description: 'The anxiety and worry are associated with three (or more) of the following six symptoms (with at least some symptoms having been present for more days than not for the past 6 months):',
      required: true,
      type: 'symptom',
      subCriteria: [
        {
          id: 'gad-c1',
          code: 'C1',
          description: 'Restlessness or feeling keyed up or on edge',
          required: false,
          type: 'symptom'
        },
        {
          id: 'gad-c2',
          code: 'C2',
          description: 'Being easily fatigued',
          required: false,
          type: 'symptom'
        },
        {
          id: 'gad-c3',
          code: 'C3',
          description: 'Difficulty concentrating or mind going blank',
          required: false,
          type: 'symptom'
        },
        {
          id: 'gad-c4',
          code: 'C4',
          description: 'Irritability',
          required: false,
          type: 'symptom'
        },
        {
          id: 'gad-c5',
          code: 'C5',
          description: 'Muscle tension',
          required: false,
          type: 'symptom'
        },
        {
          id: 'gad-c6',
          code: 'C6',
          description: 'Sleep disturbance (difficulty falling or staying asleep, or restless, unsatisfying sleep)',
          required: false,
          type: 'symptom'
        }
      ]
    },
    {
      id: 'gad-d',
      code: 'D',
      description: 'The anxiety, worry, or physical symptoms cause clinically significant distress or impairment in social, occupational, or other important areas of functioning.',
      required: true,
      type: 'functional'
    },
    {
      id: 'gad-e',
      code: 'E',
      description: 'The disturbance is not attributable to the physiological effects of a substance (e.g., a drug of abuse, a medication) or another medical condition (e.g., hyperthyroidism).',
      required: true,
      type: 'exclusion'
    },
    {
      id: 'gad-f',
      code: 'F',
      description: 'The disturbance is not better explained by another mental disorder.',
      required: true,
      type: 'exclusion'
    }
  ],
  specifiers: [
    {
      id: 'severity',
      name: 'Severity',
      description: 'Current severity of generalized anxiety disorder',
      type: 'severity',
      options: ['Mild', 'Moderate', 'Severe'],
      required: true
    }
  ],
  diagnosticFeatures: [
    'Excessive anxiety and worry for at least 6 months',
    'Difficulty controlling worry',
    'Associated physical symptoms',
    'Significant functional impairment'
  ],
  prevalence: '2.9% 12-month prevalence in the United States',
  minimumCriteria: 6,
  durationRequirement: '6 months',
  exclusionCriteria: ['Substance use', 'Medical condition', 'Other mental disorders']
};

export const PANIC_DISORDER: Disorder = {
  id: 'panic-disorder',
  code: '300.01',
  name: 'Panic Disorder',
  category: 'anxiety',
  description: 'Recurrent unexpected panic attacks followed by at least 1 month of persistent concern or worry about additional panic attacks or their consequences.',
  criteria: [
    {
      id: 'pd-a',
      code: 'A',
      description: 'Recurrent unexpected panic attacks. A panic attack is an abrupt surge of intense fear or intense discomfort that reaches a peak within minutes, and during which time four (or more) of the following symptoms occur:',
      required: true,
      type: 'symptom',
      subCriteria: [
        {
          id: 'pd-a1',
          code: 'A1',
          description: 'Palpitations, pounding heart, or accelerated heart rate',
          required: false,
          type: 'symptom'
        },
        {
          id: 'pd-a2',
          code: 'A2',
          description: 'Sweating',
          required: false,
          type: 'symptom'
        },
        {
          id: 'pd-a3',
          code: 'A3',
          description: 'Trembling or shaking',
          required: false,
          type: 'symptom'
        },
        {
          id: 'pd-a4',
          code: 'A4',
          description: 'Sensations of shortness of breath or smothering',
          required: false,
          type: 'symptom'
        },
        {
          id: 'pd-a5',
          code: 'A5',
          description: 'Feelings of choking',
          required: false,
          type: 'symptom'
        },
        {
          id: 'pd-a6',
          code: 'A6',
          description: 'Chest pain or discomfort',
          required: false,
          type: 'symptom'
        },
        {
          id: 'pd-a7',
          code: 'A7',
          description: 'Nausea or abdominal distress',
          required: false,
          type: 'symptom'
        },
        {
          id: 'pd-a8',
          code: 'A8',
          description: 'Feeling dizzy, unsteady, light-headed, or faint',
          required: false,
          type: 'symptom'
        },
        {
          id: 'pd-a9',
          code: 'A9',
          description: 'Chills or heat sensations',
          required: false,
          type: 'symptom'
        },
        {
          id: 'pd-a10',
          code: 'A10',
          description: 'Paresthesias (numbness or tingling sensations)',
          required: false,
          type: 'symptom'
        },
        {
          id: 'pd-a11',
          code: 'A11',
          description: 'Derealization (feelings of unreality) or depersonalization (being detached from oneself)',
          required: false,
          type: 'symptom'
        },
        {
          id: 'pd-a12',
          code: 'A12',
          description: 'Fear of losing control or "going crazy"',
          required: false,
          type: 'symptom'
        },
        {
          id: 'pd-a13',
          code: 'A13',
          description: 'Fear of dying',
          required: false,
          type: 'symptom'
        }
      ]
    },
    {
      id: 'pd-b',
      code: 'B',
      description: 'At least one of the attacks has been followed by 1 month (or more) of one or both of the following: (1) persistent concern or worry about additional panic attacks or their consequences, (2) a significant maladaptive change in behavior related to the attacks.',
      required: true,
      type: 'symptom'
    },
    {
      id: 'pd-c',
      code: 'C',
      description: 'The disturbance is not attributable to the physiological effects of a substance or another medical condition.',
      required: true,
      type: 'exclusion'
    },
    {
      id: 'pd-d',
      code: 'D',
      description: 'The disturbance is not better explained by another mental disorder.',
      required: true,
      type: 'exclusion'
    }
  ],
  specifiers: [],
  diagnosticFeatures: [
    'Recurrent unexpected panic attacks',
    'Persistent concern about future attacks',
    'Significant behavioral changes',
    'Peak intensity within minutes'
  ],
  prevalence: '2.7% 12-month prevalence in the United States',
  minimumCriteria: 4,
  durationRequirement: '1 month of concern following attack',
  exclusionCriteria: ['Substance use', 'Medical condition', 'Other mental disorders']
};

export const ANXIETY_DISORDERS: Disorder[] = [
  GENERALIZED_ANXIETY_DISORDER,
  PANIC_DISORDER
];
