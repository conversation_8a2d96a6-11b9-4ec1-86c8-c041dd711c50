import type { Disorder } from '../types';

export const OBSESSIVE_COMPULSIVE_DISORDER: Disorder = {
  id: 'ocd',
  code: '300.3',
  name: 'Obsessive-Compulsive Disorder',
  category: 'obsessive-compulsive',
  description: 'Presence of obsessions, compulsions, or both that are time-consuming or cause clinically significant distress or impairment.',
  criteria: [
    {
      id: 'ocd-a',
      code: 'A',
      description: 'Presence of obsessions, compulsions, or both:',
      required: true,
      type: 'symptom',
      subCriteria: [
        {
          id: 'ocd-obsessions',
          code: 'Obsessions',
          description: 'Obsessions are defined by (1) and (2):',
          required: false,
          type: 'symptom',
          subCriteria: [
            {
              id: 'ocd-obs-1',
              code: '1',
              description: 'Recurrent and persistent thoughts, urges, or images that are experienced, at some time during the disturbance, as intrusive and unwanted, and that in most individuals cause marked anxiety or distress',
              required: false,
              type: 'symptom'
            },
            {
              id: 'ocd-obs-2',
              code: '2',
              description: 'The individual attempts to ignore or suppress such thoughts, urges, or images, or to neutralize them with some other thought or action (i.e., by performing a compulsion)',
              required: false,
              type: 'symptom'
            }
          ]
        },
        {
          id: 'ocd-compulsions',
          code: 'Compulsions',
          description: 'Compulsions are defined by (1) and (2):',
          required: false,
          type: 'symptom',
          subCriteria: [
            {
              id: 'ocd-comp-1',
              code: '1',
              description: 'Repetitive behaviors (e.g., hand washing, ordering, checking) or mental acts (e.g., praying, counting, repeating words silently) that the individual feels driven to perform in response to an obsession or according to rules that must be applied rigidly',
              required: false,
              type: 'symptom'
            },
            {
              id: 'ocd-comp-2',
              code: '2',
              description: 'The behaviors or mental acts are aimed at preventing or reducing anxiety or distress, or preventing some dreaded event or situation; however, these behaviors or mental acts are not connected in a realistic way with what they are designed to neutralize or prevent, or are clearly excessive',
              required: false,
              type: 'symptom'
            }
          ]
        }
      ]
    },
    {
      id: 'ocd-b',
      code: 'B',
      description: 'The obsessions or compulsions are time-consuming (e.g., take more than 1 hour per day) or cause clinically significant distress or impairment in social, occupational, or other important areas of functioning.',
      required: true,
      type: 'functional'
    },
    {
      id: 'ocd-c',
      code: 'C',
      description: 'The obsessive-compulsive symptoms are not attributable to the physiological effects of a substance (e.g., a drug of abuse, a medication) or another medical condition.',
      required: true,
      type: 'exclusion'
    },
    {
      id: 'ocd-d',
      code: 'D',
      description: 'The disturbance is not better explained by the symptoms of another mental disorder.',
      required: true,
      type: 'exclusion'
    }
  ],
  specifiers: [
    {
      id: 'insight',
      name: 'Insight',
      description: 'Level of insight regarding OCD beliefs',
      type: 'insight',
      options: [
        'With good or fair insight',
        'With poor insight', 
        'With absent insight/delusional beliefs'
      ],
      required: true
    },
    {
      id: 'tic-related',
      name: 'Tic-related',
      description: 'Individual has a current or past history of a tic disorder',
      type: 'features',
      options: ['Present'],
      required: false
    }
  ],
  diagnosticFeatures: [
    'Presence of obsessions and/or compulsions',
    'Time-consuming (>1 hour/day) or significant distress',
    'Not due to substance or medical condition',
    'Not better explained by another mental disorder'
  ],
  prevalence: '1.2% 12-month prevalence in the United States',
  minimumCriteria: 4,
  durationRequirement: 'No specific duration requirement',
  exclusionCriteria: ['Substance use', 'Medical condition', 'Other mental disorders']
};

export const BODY_DYSMORPHIC_DISORDER: Disorder = {
  id: 'bdd',
  code: '300.7',
  name: 'Body Dysmorphic Disorder',
  category: 'obsessive-compulsive',
  description: 'Preoccupation with one or more perceived defects or flaws in physical appearance that are not observable or appear minor to others.',
  criteria: [
    {
      id: 'bdd-a',
      code: 'A',
      description: 'Preoccupation with one or more perceived defects or flaws in physical appearance that are not observable or appear minor to others.',
      required: true,
      type: 'symptom'
    },
    {
      id: 'bdd-b',
      code: 'B',
      description: 'At some point during the course of the disorder, the individual has performed repetitive behaviors (e.g., mirror checking, excessive grooming, skin picking, seeking reassurance) or mental acts (e.g., comparing his or her appearance with that of others) in response to the appearance concerns.',
      required: true,
      type: 'symptom'
    },
    {
      id: 'bdd-c',
      code: 'C',
      description: 'The preoccupation causes clinically significant distress or impairment in social, occupational, or other important areas of functioning.',
      required: true,
      type: 'functional'
    },
    {
      id: 'bdd-d',
      code: 'D',
      description: 'The appearance preoccupations are not better explained by concerns with body fat or weight in an individual whose symptoms meet diagnostic criteria for an eating disorder.',
      required: true,
      type: 'exclusion'
    }
  ],
  specifiers: [
    {
      id: 'muscle-dysmorphia',
      name: 'Muscle dysmorphia',
      description: 'Individual is preoccupied with the idea that his or her body build is too small or insufficiently muscular',
      type: 'features',
      options: ['Present'],
      required: false
    },
    {
      id: 'insight-bdd',
      name: 'Insight',
      description: 'Level of insight regarding body dysmorphic disorder beliefs',
      type: 'insight',
      options: [
        'With good or fair insight',
        'With poor insight',
        'With absent insight/delusional beliefs'
      ],
      required: true
    }
  ],
  diagnosticFeatures: [
    'Preoccupation with perceived appearance defects',
    'Repetitive behaviors or mental acts',
    'Clinically significant distress or impairment',
    'Not better explained by eating disorder concerns'
  ],
  prevalence: '2.4% 12-month prevalence in the United States',
  minimumCriteria: 4,
  durationRequirement: 'No specific duration requirement',
  exclusionCriteria: ['Eating disorders']
};

export const HOARDING_DISORDER: Disorder = {
  id: 'hoarding',
  code: '300.3',
  name: 'Hoarding Disorder',
  category: 'obsessive-compulsive',
  description: 'Persistent difficulty discarding or parting with possessions, regardless of their actual value.',
  criteria: [
    {
      id: 'hoard-a',
      code: 'A',
      description: 'Persistent difficulty discarding or parting with possessions, regardless of their actual value.',
      required: true,
      type: 'symptom'
    },
    {
      id: 'hoard-b',
      code: 'B',
      description: 'This difficulty is due to a perceived need to save the items and to distress associated with discarding them.',
      required: true,
      type: 'symptom'
    },
    {
      id: 'hoard-c',
      code: 'C',
      description: 'The difficulty discarding possessions results in the accumulation of possessions that congest and clutter active living areas and substantially compromises their intended use. If living areas are uncluttered, it is only because of the interventions of third parties (e.g., family members, cleaners, authorities).',
      required: true,
      type: 'functional'
    },
    {
      id: 'hoard-d',
      code: 'D',
      description: 'The hoarding causes clinically significant distress or impairment in social, occupational, or other important areas of functioning (including maintaining a safe environment for self and others).',
      required: true,
      type: 'functional'
    },
    {
      id: 'hoard-e',
      code: 'E',
      description: 'The hoarding is not attributable to another medical condition (e.g., brain injury, cerebrovascular disease, Prader-Willi syndrome).',
      required: true,
      type: 'exclusion'
    },
    {
      id: 'hoard-f',
      code: 'F',
      description: 'The hoarding is not better explained by the symptoms of another mental disorder.',
      required: true,
      type: 'exclusion'
    }
  ],
  specifiers: [
    {
      id: 'excessive-acquisition',
      name: 'With excessive acquisition',
      description: 'If difficulty discarding possessions is accompanied by excessive acquisition of items that are not needed or for which there is no available space',
      type: 'features',
      options: ['Present'],
      required: false
    },
    {
      id: 'insight-hoarding',
      name: 'Insight',
      description: 'Level of insight regarding hoarding disorder beliefs and behaviors',
      type: 'insight',
      options: [
        'With good or fair insight',
        'With poor insight',
        'With absent insight/delusional beliefs'
      ],
      required: true
    }
  ],
  diagnosticFeatures: [
    'Persistent difficulty discarding possessions',
    'Perceived need to save items',
    'Accumulation and clutter of living areas',
    'Clinically significant distress or impairment',
    'Not due to medical condition or other mental disorder'
  ],
  prevalence: '2.6% 12-month prevalence in the United States',
  minimumCriteria: 6,
  durationRequirement: 'No specific duration requirement',
  exclusionCriteria: ['Medical conditions', 'Other mental disorders']
};

export const OBSESSIVE_COMPULSIVE_DISORDERS: Disorder[] = [
  OBSESSIVE_COMPULSIVE_DISORDER,
  BODY_DYSMORPHIC_DISORDER,
  HOARDING_DISORDER
];
