import type { TestDefinition } from '../types';

export const PHQ9_TEST: TestDefinition = {
  id: 'phq-9',
  name: 'Patient Health Questionnaire-9 (PHQ-9)',
  category: 'depression',
  description: 'A 9-item screening tool for depression severity based on DSM-5 criteria.',
  instructions: 'Over the last 2 weeks, how often have you been bothered by any of the following problems?',
  timeLimit: 10,
  items: [
    {
      id: 1,
      question: 'Little interest or pleasure in doing things',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'anhedonia'
    },
    {
      id: 2,
      question: 'Feeling down, depressed, or hopeless',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'mood'
    },
    {
      id: 3,
      question: 'Trouble falling or staying asleep, or sleeping too much',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'sleep'
    },
    {
      id: 4,
      question: 'Feeling tired or having little energy',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'energy'
    },
    {
      id: 5,
      question: 'Poor appetite or overeating',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'appetite'
    },
    {
      id: 6,
      question: 'Feeling bad about yourself - or that you are a failure or have let yourself or your family down',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'self_worth'
    },
    {
      id: 7,
      question: 'Trouble concentrating on things, such as reading the newspaper or watching television',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'concentration'
    },
    {
      id: 8,
      question: 'Moving or speaking so slowly that other people could have noticed? Or the opposite - being so fidgety or restless that you have been moving around a lot more than usual',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'psychomotor'
    },
    {
      id: 9,
      question: 'Thoughts that you would be better off dead, or of hurting yourself in some way',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'suicidal_ideation',
      riskFlag: true
    }
  ],
  scoringRules: {
    totalScore: {
      method: 'sum'
    }
  },
  interpretationRules: {
    severity: {
      ranges: [
        {
          min: 0,
          max: 4,
          label: 'Minimal Depression',
          description: 'Minimal or no depression symptoms',
          color: '#10B981'
        },
        {
          min: 5,
          max: 9,
          label: 'Mild Depression',
          description: 'Mild depression symptoms',
          color: '#F59E0B'
        },
        {
          min: 10,
          max: 14,
          label: 'Moderate Depression',
          description: 'Moderate depression symptoms',
          color: '#EF4444'
        },
        {
          min: 15,
          max: 19,
          label: 'Moderately Severe Depression',
          description: 'Moderately severe depression symptoms',
          color: '#DC2626'
        },
        {
          min: 20,
          max: 27,
          label: 'Severe Depression',
          description: 'Severe depression symptoms',
          color: '#991B1B'
        }
      ]
    },
    clinicalCutoffs: {
      'clinical_depression': {
        threshold: 10,
        direction: 'above',
        meaning: 'Indicates clinically significant depression requiring treatment'
      },
      'major_depression': {
        threshold: 15,
        direction: 'above',
        meaning: 'Suggests major depressive disorder'
      }
    },
    recommendations: {
      'Minimal Depression': [
        'Continue routine monitoring',
        'Promote wellness activities',
        'Consider preventive interventions if risk factors present'
      ],
      'Mild Depression': [
        'Psychoeducation about depression',
        'Lifestyle interventions (exercise, sleep hygiene)',
        'Consider brief counseling or support groups',
        'Monitor for progression'
      ],
      'Moderate Depression': [
        'Recommend psychotherapy (CBT, IPT)',
        'Consider antidepressant medication',
        'Regular follow-up appointments',
        'Assess for suicidal ideation',
        'Consider referral to mental health specialist'
      ],
      'Moderately Severe Depression': [
        'Strongly recommend combination therapy (medication + psychotherapy)',
        'Close monitoring and frequent follow-ups',
        'Assess safety and suicidal risk',
        'Consider intensive outpatient treatment',
        'Referral to psychiatrist recommended'
      ],
      'Severe Depression': [
        'Immediate psychiatric evaluation required',
        'Combination therapy with medication and psychotherapy',
        'Consider intensive treatment options (IOP, PHP, inpatient)',
        'Safety assessment and risk management',
        'Close monitoring and crisis planning'
      ]
    }
  },
  validityChecks: [
    {
      type: 'response_pattern',
      rule: 'all_same_response',
      message: 'All responses are identical - validity questionable'
    },
    {
      type: 'consistency',
      rule: 'extreme_responding',
      threshold: 0.8,
      message: 'Extreme responding pattern detected'
    }
  ]
};

// Scoring function
export function scorePHQ9(responses: number[]): {
  totalScore: number;
  severity: string;
  clinicalRange: string;
  riskFlags: Array<{ type: string; level: string; description: string }>;
  interpretation: string;
  recommendations: string[];
} {
  if (responses.length !== 9) {
    throw new Error('PHQ-9 requires exactly 9 responses');
  }

  const totalScore = responses.reduce((sum, score) => sum + score, 0);
  
  // Determine severity
  let severity = 'Minimal Depression';
  let clinicalRange = 'normal';
  
  if (totalScore >= 20) {
    severity = 'Severe Depression';
    clinicalRange = 'clinical';
  } else if (totalScore >= 15) {
    severity = 'Moderately Severe Depression';
    clinicalRange = 'clinical';
  } else if (totalScore >= 10) {
    severity = 'Moderate Depression';
    clinicalRange = 'clinical';
  } else if (totalScore >= 5) {
    severity = 'Mild Depression';
    clinicalRange = 'borderline';
  }

  // Check for risk flags
  const riskFlags = [];
  if (responses[8] > 0) { // Item 9 - suicidal ideation
    const riskLevel = responses[8] === 1 ? 'moderate' : responses[8] === 2 ? 'high' : 'high';
    riskFlags.push({
      type: 'suicide',
      level: riskLevel,
      description: `Suicidal ideation reported: ${PHQ9_TEST.items[8].options[responses[8]]}`
    });
  }

  // Generate interpretation
  const interpretation = `PHQ-9 total score of ${totalScore} indicates ${severity.toLowerCase()}. ` +
    (totalScore >= 10 ? 'This score suggests clinically significant depression that may benefit from treatment. ' : '') +
    (riskFlags.length > 0 ? 'IMPORTANT: Suicidal ideation was reported and requires immediate clinical attention. ' : '') +
    'Consider the patient\'s clinical presentation and other factors when making treatment decisions.';

  // Get recommendations
  const recommendations = PHQ9_TEST.interpretationRules.recommendations?.[severity] || [];

  return {
    totalScore,
    severity,
    clinicalRange,
    riskFlags,
    interpretation,
    recommendations
  };
}
