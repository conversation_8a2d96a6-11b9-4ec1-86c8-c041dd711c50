import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Brain, FileText, TrendingUp, Calendar, Plus, User } from 'lucide-react';
import { usePatientStore } from '../../store/patientStore';
import { PHQ9 } from './PHQ9';
import { GAD7 } from './GAD7';

export const PsychTestingDashboard = () => {
  const { selectedPatient } = usePatientStore();
  const [activeTab, setActiveTab] = useState('overview');
  const [currentTest, setCurrentTest] = useState<string | null>(null);

  if (!selectedPatient) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <User className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-900 mb-2">No Patient Selected</h3>
          <p className="text-slate-600">Please select a patient to view psychological testing options.</p>
        </CardContent>
      </Card>
    );
  }

  const quickScreeners = [
    {
      name: 'PHQ-9',
      description: 'Depression Screening',
      category: 'depression',
      duration: '5 min',
      icon: Brain,
      color: 'bg-blue-500'
    },
    {
      name: 'GAD-7',
      description: 'Anxiety Screening',
      category: 'anxiety',
      duration: '3 min',
      icon: Brain,
      color: 'bg-green-500'
    },
    {
      name: 'Y-BOCS',
      description: 'OCD Assessment',
      category: 'ocd',
      duration: '15 min',
      icon: Brain,
      color: 'bg-purple-500'
    },
    {
      name: 'MMSE',
      description: 'Cognitive Screening',
      category: 'cognitive',
      duration: '10 min',
      icon: Brain,
      color: 'bg-orange-500'
    }
  ];

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FileText },
    { id: 'quick-screeners', label: 'Quick Screeners', icon: Brain },
    { id: 'test-history', label: 'Test History', icon: Calendar },
    { id: 'reports', label: 'Reports', icon: TrendingUp }
  ];

  const handleStartTest = (testName: string) => {
    setCurrentTest(testName);
  };

  const handleBackToDashboard = () => {
    setCurrentTest(null);
  };

  const handleSaveTest = async (testData: any) => {
    try {
      // Here you would call the API to save the test
      console.log('Saving test data:', testData);

      // For now, just show success and go back to dashboard
      alert('Test results saved successfully!');
      setCurrentTest(null);
      setActiveTab('test-history');
    } catch (error) {
      console.error('Error saving test:', error);
      alert('Error saving test results. Please try again.');
    }
  };

  // If a test is active, render the test component
  if (currentTest && selectedPatient) {
    switch (currentTest) {
      case 'PHQ-9':
        return (
          <PHQ9
            onBack={handleBackToDashboard}
            onSave={handleSaveTest}
            patientId={selectedPatient.id}
          />
        );
      case 'GAD-7':
        return (
          <GAD7
            onBack={handleBackToDashboard}
            onSave={handleSaveTest}
            patientId={selectedPatient.id}
          />
        );
      default:
        return <div>Test not implemented yet</div>;
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Psychological Testing</h1>
          <p className="text-gray-600 mt-1">
            Patient: {selectedPatient.firstName} {selectedPatient.lastName} ({selectedPatient.patientId})
          </p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          New Assessment
        </Button>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Brain className="h-5 w-5 mr-2 text-blue-500" />
                Recent Tests
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">No recent psychological tests found.</p>
              <Button variant="outline" className="mt-3 w-full">
                View All Tests
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-green-500" />
                Progress Tracking
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Track symptom changes over time.</p>
              <Button variant="outline" className="mt-3 w-full">
                View Trends
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2 text-purple-500" />
                Test Reports
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Generate comprehensive reports.</p>
              <Button variant="outline" className="mt-3 w-full">
                Create Report
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'quick-screeners' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickScreeners.map((screener) => {
            const Icon = screener.icon;
            return (
              <Card key={screener.name} className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-lg ${screener.color}`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <span className="text-sm text-gray-500">{screener.duration}</span>
                  </div>
                  <h3 className="font-semibold text-lg mb-2">{screener.name}</h3>
                  <p className="text-gray-600 text-sm mb-4">{screener.description}</p>
                  <Button
                    className="w-full"
                    size="sm"
                    onClick={() => handleStartTest(screener.name)}
                  >
                    Start Test
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {activeTab === 'test-history' && (
        <Card>
          <CardHeader>
            <CardTitle>Test History</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Test History</h3>
              <p className="text-gray-600 mb-4">This patient has no psychological test history yet.</p>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Administer First Test
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'reports' && (
        <Card>
          <CardHeader>
            <CardTitle>Test Reports</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Reports Available</h3>
              <p className="text-gray-600 mb-4">Generate reports after completing psychological tests.</p>
              <Button disabled>
                Generate Report
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
