import { useState, useCallback } from 'react';
import { AxiosError } from 'axios';

interface ApiErrorState {
  error: string | null;
  isLoading: boolean;
  retryCount: number;
}

interface UseApiErrorOptions {
  maxRetries?: number;
  retryDelay?: number;
  onError?: (error: Error) => void;
  onRetry?: (retryCount: number) => void;
}

/**
 * Hook for handling API errors with retry functionality and user-friendly error messages
 */
export const useApiError = (options: UseApiErrorOptions = {}) => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    onError,
    onRetry
  } = options;

  const [state, setState] = useState<ApiErrorState>({
    error: null,
    isLoading: false,
    retryCount: 0
  });

  const getErrorMessage = useCallback((error: unknown): string => {
    if (error instanceof AxiosError) {
      // Handle specific HTTP status codes
      switch (error.response?.status) {
        case 401:
          return 'Authentication required. Please log in again.';
        case 403:
          return 'You do not have permission to perform this action.';
        case 404:
          return 'The requested resource was not found.';
        case 409:
          return 'This action conflicts with existing data.';
        case 422:
          return 'The provided data is invalid. Please check your input.';
        case 429:
          return 'Too many requests. Please wait a moment and try again.';
        case 500:
          return 'Server error. Please try again later.';
        case 502:
        case 503:
        case 504:
          return 'Service temporarily unavailable. Please try again.';
        default:
          if (error.response?.data?.message) {
            return error.response.data.message;
          }
          if (error.response?.data?.error) {
            return error.response.data.error;
          }
      }

      // Handle network errors
      if (error.code === 'ECONNABORTED') {
        return 'Request timeout. Please check your connection and try again.';
      }
      if (error.code === 'ERR_NETWORK') {
        return 'Network error. Please check your internet connection.';
      }
      if (!error.response) {
        return 'Unable to connect to the server. Please check your connection.';
      }
    }

    if (error instanceof Error) {
      return error.message;
    }

    return 'An unexpected error occurred. Please try again.';
  }, []);

  const isRetryableError = useCallback((error: unknown): boolean => {
    if (error instanceof AxiosError) {
      // Don't retry client errors (4xx) except for specific cases
      const status = error.response?.status;
      if (status && status >= 400 && status < 500) {
        // Only retry these specific client errors
        return status === 408 || status === 429; // Timeout or Rate Limited
      }

      // Retry server errors (5xx) and network errors
      return !error.response || (status !== undefined && status >= 500);
    }

    return false;
  }, []);

  const executeWithRetry = useCallback(async <T>(
    apiCall: () => Promise<T>,
    customRetries?: number
  ): Promise<T> => {
    const maxAttempts = customRetries ?? maxRetries;
    let lastError: unknown;

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    for (let attempt = 0; attempt <= maxAttempts; attempt++) {
      try {
        const result = await apiCall();
        setState(prev => ({ ...prev, isLoading: false, error: null, retryCount: 0 }));
        return result;
      } catch (error) {
        lastError = error;
        
        // Log error for debugging
        console.error(`API call attempt ${attempt + 1} failed:`, error);

        // Don't retry on the last attempt or if error is not retryable
        if (attempt === maxAttempts || !isRetryableError(error)) {
          break;
        }

        // Update retry count and notify
        setState(prev => ({ ...prev, retryCount: attempt + 1 }));
        if (onRetry) {
          onRetry(attempt + 1);
        }

        // Wait before retrying with exponential backoff
        const delay = retryDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // All retries failed
    const errorMessage = getErrorMessage(lastError);
    setState(prev => ({ 
      ...prev, 
      isLoading: false, 
      error: errorMessage,
      retryCount: 0
    }));

    if (onError && lastError instanceof Error) {
      onError(lastError);
    }

    throw lastError;
  }, [maxRetries, retryDelay, onError, onRetry, getErrorMessage, isRetryableError]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const retry = useCallback(async <T>(apiCall: () => Promise<T>): Promise<T> => {
    return executeWithRetry(apiCall, 1); // Single retry
  }, [executeWithRetry]);

  return {
    error: state.error,
    isLoading: state.isLoading,
    retryCount: state.retryCount,
    executeWithRetry,
    clearError,
    retry,
    getErrorMessage
  };
};

export default useApiError;
