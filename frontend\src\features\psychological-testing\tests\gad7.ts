import type { TestDefinition } from '../types';

export const GAD7_TEST: TestDefinition = {
  id: 'gad-7',
  name: 'Generalized Anxiety Disorder 7-item (GAD-7)',
  category: 'anxiety',
  description: 'A 7-item screening tool for generalized anxiety disorder.',
  instructions: 'Over the last 2 weeks, how often have you been bothered by the following problems?',
  timeLimit: 5,
  items: [
    {
      id: 1,
      question: 'Feeling nervous, anxious, or on edge',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'anxiety'
    },
    {
      id: 2,
      question: 'Not being able to stop or control worrying',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'worry_control'
    },
    {
      id: 3,
      question: 'Worrying too much about different things',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'excessive_worry'
    },
    {
      id: 4,
      question: 'Trouble relaxing',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'relaxation'
    },
    {
      id: 5,
      question: 'Being so restless that it is hard to sit still',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'restlessness'
    },
    {
      id: 6,
      question: 'Becoming easily annoyed or irritable',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'irritability'
    },
    {
      id: 7,
      question: 'Feeling afraid, as if something awful might happen',
      options: ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
      scores: [0, 1, 2, 3],
      category: 'fear'
    }
  ],
  scoringRules: {
    totalScore: {
      method: 'sum'
    }
  },
  interpretationRules: {
    severity: {
      ranges: [
        {
          min: 0,
          max: 4,
          label: 'Minimal Anxiety',
          description: 'Minimal or no anxiety symptoms',
          color: '#10B981'
        },
        {
          min: 5,
          max: 9,
          label: 'Mild Anxiety',
          description: 'Mild anxiety symptoms',
          color: '#F59E0B'
        },
        {
          min: 10,
          max: 14,
          label: 'Moderate Anxiety',
          description: 'Moderate anxiety symptoms',
          color: '#EF4444'
        },
        {
          min: 15,
          max: 21,
          label: 'Severe Anxiety',
          description: 'Severe anxiety symptoms',
          color: '#991B1B'
        }
      ]
    },
    clinicalCutoffs: {
      'clinical_anxiety': {
        threshold: 10,
        direction: 'above',
        meaning: 'Indicates clinically significant anxiety requiring treatment'
      },
      'gad_probable': {
        threshold: 15,
        direction: 'above',
        meaning: 'Highly suggestive of Generalized Anxiety Disorder'
      }
    },
    recommendations: {
      'Minimal Anxiety': [
        'Continue routine monitoring',
        'Promote stress management techniques',
        'Consider preventive interventions if risk factors present'
      ],
      'Mild Anxiety': [
        'Psychoeducation about anxiety',
        'Stress management and relaxation techniques',
        'Consider brief counseling or support groups',
        'Monitor for progression'
      ],
      'Moderate Anxiety': [
        'Recommend psychotherapy (CBT, GAD-specific interventions)',
        'Consider anti-anxiety medication',
        'Regular follow-up appointments',
        'Assess functional impairment',
        'Consider referral to mental health specialist'
      ],
      'Severe Anxiety': [
        'Immediate psychiatric evaluation recommended',
        'Combination therapy (medication + psychotherapy)',
        'Close monitoring and frequent follow-ups',
        'Assess for panic disorder and other anxiety disorders',
        'Consider intensive treatment options if needed'
      ]
    }
  },
  validityChecks: [
    {
      type: 'response_pattern',
      rule: 'all_same_response',
      message: 'All responses are identical - validity questionable'
    },
    {
      type: 'consistency',
      rule: 'extreme_responding',
      threshold: 0.8,
      message: 'Extreme responding pattern detected'
    }
  ]
};

// Scoring function
export function scoreGAD7(responses: number[]): {
  totalScore: number;
  severity: string;
  clinicalRange: string;
  interpretation: string;
  recommendations: string[];
} {
  if (responses.length !== 7) {
    throw new Error('GAD-7 requires exactly 7 responses');
  }

  const totalScore = responses.reduce((sum, score) => sum + score, 0);
  
  // Determine severity
  let severity = 'Minimal Anxiety';
  let clinicalRange = 'normal';
  
  if (totalScore >= 15) {
    severity = 'Severe Anxiety';
    clinicalRange = 'clinical';
  } else if (totalScore >= 10) {
    severity = 'Moderate Anxiety';
    clinicalRange = 'clinical';
  } else if (totalScore >= 5) {
    severity = 'Mild Anxiety';
    clinicalRange = 'borderline';
  }

  // Generate interpretation
  const interpretation = `GAD-7 total score of ${totalScore} indicates ${severity.toLowerCase()}. ` +
    (totalScore >= 10 ? 'This score suggests clinically significant anxiety that may benefit from treatment. ' : '') +
    (totalScore >= 15 ? 'This score is highly suggestive of Generalized Anxiety Disorder. ' : '') +
    'Consider the patient\'s clinical presentation and other factors when making treatment decisions.';

  // Get recommendations
  const recommendations = GAD7_TEST.interpretationRules.recommendations?.[severity] || [];

  return {
    totalScore,
    severity,
    clinicalRange,
    interpretation,
    recommendations
  };
}
