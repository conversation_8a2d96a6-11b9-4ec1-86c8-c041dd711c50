const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const { PrismaClient } = require('@prisma/client');

const app = express();
const prisma = new PrismaClient();
const PORT = 3002;

// Environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key-change-this-in-production';

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  if (req.headers.authorization) {
    console.log('Authorization header present:', req.headers.authorization.substring(0, 20) + '...');
  }
  next();
});

// JWT utility functions
const generateAccessToken = (payload) => {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: '15m',
    issuer: 'psychiatry-app',
    audience: 'psychiatry-app-users',
  });
};

const generateRefreshToken = (payload) => {
  return jwt.sign(payload, JWT_REFRESH_SECRET, {
    expiresIn: '7d',
    issuer: 'psychiatry-app',
    audience: 'psychiatry-app-users',
  });
};

const verifyAccessToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: 'psychiatry-app',
      audience: 'psychiatry-app-users',
    });
  } catch (error) {
    console.error('Token verification error:', error.message);
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Access token expired');
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid access token');
    }
    throw new Error('Token verification failed');
  }
};

const extractTokenFromHeader = (authHeader) => {
  if (!authHeader) {
    return null;
  }
  
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }
  
  return parts[1];
};

// Authentication middleware
const authenticate = async (req, res, next) => {
  try {
    console.log('🔐 Authentication middleware triggered');
    console.log('Headers:', req.headers);
    
    const token = extractTokenFromHeader(req.headers.authorization);
    console.log('Extracted token:', token ? 'Present' : 'Missing');

    if (!token) {
      console.log('❌ No token provided');
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }

    // Verify the token
    console.log('🔍 Verifying token...');
    const payload = verifyAccessToken(token);
    console.log('✅ Token verified, payload:', { userId: payload.userId, username: payload.username });

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        isActive: true,
        lockedUntil: true,
      },
    });

    if (!user) {
      console.log('❌ User not found in database');
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    if (!user.isActive) {
      console.log('❌ User account deactivated');
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }

    if (user.lockedUntil && user.lockedUntil > new Date()) {
      console.log('❌ User account locked');
      return res.status(401).json({
        success: false,
        message: 'Account is temporarily locked'
      });
    }

    // Add user to request object
    req.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName,
    };

    console.log('✅ Authentication successful for user:', user.username);
    next();
  } catch (error) {
    console.error('❌ Authentication error:', error.message);
    res.status(401).json({
      success: false,
      message: error.message || 'Authentication failed'
    });
  }
};

// Health check
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Auth debug server is healthy',
    timestamp: new Date().toISOString(),
  });
});

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('🔑 Login attempt:', req.body);
    
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Username and password are required'
      });
    }

    // Find user
    const user = await prisma.user.findUnique({
      where: { username },
    });

    if (!user) {
      console.log('❌ User not found:', username);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      console.log('❌ Invalid password for user:', username);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }

    // Check if account is locked
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      return res.status(401).json({
        success: false,
        message: 'Account is temporarily locked'
      });
    }

    // Generate tokens
    const tokenId = require('crypto').randomUUID();
    const accessToken = generateAccessToken({
      userId: user.id,
      username: user.username,
      role: user.role,
    });
    const refreshToken = generateRefreshToken({
      userId: user.id,
      tokenId,
    });

    console.log('✅ Login successful for user:', username);
    console.log('Generated access token length:', accessToken.length);

    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName,
        },
        accessToken,
        refreshToken,
      },
      message: 'Login successful',
    });

  } catch (error) {
    console.error('❌ Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Protected endpoint test
app.get('/api/patients', authenticate, async (req, res) => {
  try {
    console.log('📋 Fetching patients for user:', req.user.username);
    
    const patients = await prisma.patient.findMany({
      where: { isDeleted: false },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        dateOfBirth: true,
        gender: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log('✅ Found', patients.length, 'patients');

    res.status(200).json({
      success: true,
      data: { patients },
      message: 'Patients retrieved successfully'
    });
  } catch (error) {
    console.error('❌ Error fetching patients:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch patients'
    });
  }
});

// Dashboard analytics test
app.get('/api/analytics/dashboard', authenticate, async (req, res) => {
  try {
    console.log('📊 Fetching dashboard analytics for user:', req.user.username);
    
    const totalPatients = await prisma.patient.count({ where: { isDeleted: false } });
    const totalAppointments = await prisma.appointment.count({ where: { isDeleted: false } });
    const totalLabResults = await prisma.labResult.count({ where: { isDeleted: false } });

    const analytics = {
      totalPatients,
      totalAppointments,
      totalLabResults,
      flaggedLabResults: 0, // Simplified for now
    };

    console.log('✅ Dashboard analytics:', analytics);

    res.status(200).json({
      success: true,
      data: analytics,
      message: 'Dashboard analytics retrieved successfully'
    });
  } catch (error) {
    console.error('❌ Error fetching dashboard analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard analytics'
    });
  }
});

// Error handler
app.use((error, req, res, next) => {
  console.error('❌ Unhandled error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 AUTH DEBUG SERVER RUNNING');
  console.log(`📍 Port: ${PORT}`);
  console.log(`🔗 Health: http://localhost:${PORT}/health`);
  console.log(`🔑 Login: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`📋 Patients: GET http://localhost:${PORT}/api/patients`);
  console.log(`📊 Analytics: GET http://localhost:${PORT}/api/analytics/dashboard`);
  console.log('');
  console.log('🔍 DEBUGGING AUTHENTICATION SYSTEM...');
});

module.exports = app;
