const axios = require('axios');

async function debugAuth() {
  console.log('🔍 DEBUGGING AUTHENTICATION SYSTEM');
  console.log('=' .repeat(50));

  try {
    // Test 1: Health check
    console.log('\n1. Testing health endpoint...');
    const healthResponse = await axios.get('http://localhost:3002/health');
    console.log('✅ Health check:', healthResponse.status, healthResponse.data.message);

    // Test 2: API documentation
    console.log('\n2. Testing API documentation...');
    const apiResponse = await axios.get('http://localhost:3002/api');
    console.log('✅ API docs:', apiResponse.status, 'Available endpoints:', Object.keys(apiResponse.data.endpoints));

    // Test 3: Login attempt
    console.log('\n3. Testing login endpoint...');
    const loginData = {
      username: 'admin',
      password: 'admin123'
    };

    console.log('Login payload:', loginData);
    
    const loginResponse = await axios.post('http://localhost:3002/api/auth/login', loginData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('✅ Login successful!');
    console.log('Status:', loginResponse.status);
    console.log('Response structure:', Object.keys(loginResponse.data));
    
    if (loginResponse.data.data && loginResponse.data.data.accessToken) {
      const token = loginResponse.data.data.accessToken;
      console.log('✅ Access token received (length:', token.length, ')');
      
      // Test 4: Use token to access protected endpoint
      console.log('\n4. Testing protected endpoint with token...');
      const patientsResponse = await axios.get('http://localhost:3002/api/patients', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('✅ Protected endpoint access successful!');
      console.log('Status:', patientsResponse.status);
      console.log('Patients count:', patientsResponse.data.data?.patients?.length || 0);
      
    } else {
      console.log('❌ No access token in response');
      console.log('Response data:', JSON.stringify(loginResponse.data, null, 2));
    }

  } catch (error) {
    console.error('❌ Error during authentication debug:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Status Text:', error.response.statusText);
      console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      console.error('Response Headers:', error.response.headers);
    } else if (error.request) {
      console.error('No response received');
      console.error('Request:', error.request);
    } else {
      console.error('Error:', error.message);
    }
    
    console.error('Full error:', error);
  }
}

// Run the debug
debugAuth();
